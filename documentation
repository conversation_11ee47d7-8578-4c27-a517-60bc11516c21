How to work with deezer api through rapidapi:

Sending a request to the "track" endpoint with a query parameter for the track name and artist name will return a list of tracks that match the query.:
const data = null;

const xhr = new XMLHttpRequest();
xhr.withCredentials = true;

xhr.addEventListener('readystatechange', function () {
	if (this.readyState === this.DONE) {
		console.log(this.responseText); 
	}
});

xhr.open('GET', 'https://deezerdevs-deezer.p.rapidapi.com/track/%7Bid%7D');
xhr.setRequestHeader('x-rapidapi-key', '**************************************************'); // rotated api key
xhr.setRequestHeader('x-rapidapi-host', 'deezerdevs-deezer.p.rapidapi.com');

xhr.send(data);


---------------------------
Here's a response we get from using the id from a track:

{
  "id": 116348656,
  "readable": true,
  "title": "Let It Be (Remastered 2009)",
  "title_short": "Let It Be",
  "title_version": "(Remastered 2009)",
  "isrc": "GBAYE0601713",
  "link": "https://www.deezer.com/track/116348656",
  "share": "https://www.deezer.com/track/116348656?utm_source=deezer&utm_content=track-116348656&utm_term=0_1746809963&utm_medium=web",
  "duration": 243,
  "track_position": 6,
  "disk_number": 1,
  "rank": 892118,
  "release_date": "2015-12-24",
  "explicit_lyrics": false,
  "explicit_content_lyrics": 0,
  "explicit_content_cover": 0,
  "preview": "https://cdnt-preview.dzcdn.net/api/1/1/a/f/5/0/af5b351b100513b1c167e5ed96b7ece3.mp3?hdnea=exp=1746810863~acl=/api/1/1/a/f/5/0/af5b351b100513b1c167e5ed96b7ece3.mp3*~data=user_id=0,application_id=42~hmac=e4618d6579cdc995b738e487eefd4d9ebffdaad4dc51d570b4c2bfe91f4f61ae",
  "bpm": 137.81,
  "gain": -12.2,
  "available_countries": [
    "AE",
    "AF",
    "AG",
    "AI",
    "AL",
    "AM",
    "AO",
    "AQ",
    "AR",
    "AS",
    "AT",
    "AU",
    "AZ",
    "BA",
    "BB",
    "BD",
    "BE",
    "BF",
    "BG",
    "BH",
    "BI",
    "BJ",
    "BN",
    "BO",
    "BQ",
    "BR",
    "BT",
    "BV",
    "BW",
    "BY",
    "CA",
    "CC",
    "CD",
    "CF",
    "CG",
    "CH",
    "CI",
    "CK",
    "CL",
    "CM",
    "CO",
    "CR",
    "CU",
    "CV",
    "CW",
    "CX",
    "CY",
    "CZ",
    "DE",
    "DJ",
    "DK",
    "DM",
    "DO",
    "DZ",
    "EC",
    "EE",
    "EG",
    "EH",
    "ER",
    "ES",
    "ET",
    "FI",
    "FJ",
    "FK",
    "FM",
    "FR",
    "GA",
    "GB",
    "GD",
    "GE",
    "GH",
    "GM",
    "GN",
    "GQ",
    "GR",
    "GS",
    "GT",
    "GU",
    "GW",
    "HK",
    "HM",
    "HN",
    "HR",
    "HU",
    "ID",
    "IE",
    "IL",
    "IN",
    "IQ",
    "IR",
    "IS",
    "IT",
    "JM",
    "JO",
    "JP",
    "KE",
    "KG",
    "KH",
    "KI",
    "KM",
    "KN",
    "KP",
    "KR",
    "KW",
    "KY",
    "KZ",
    "LA",
    "LB",
    "LC",
    "LK",
    "LR",
    "LS",
    "LT",
    "LU",
    "LV",
    "LY",
    "MA",
    "MD",
    "ME",
    "MG",
    "MH",
    "MK",
    "ML",
    "MM",
    "MN",
    "MP",
    "MR",
    "MS",
    "MT",
    "MU",
    "MV",
    "MW",
    "MX",
    "MY",
    "MZ",
    "NA",
    "NE",
    "NF",
    "NG",
    "NI",
    "NL",
    "NO",
    "NP",
    "NR",
    "NU",
    "NZ",
    "OM",
    "PA",
    "PE",
    "PG",
    "PH",
    "PK",
    "PL",
    "PN",
    "PS",
    "PT",
    "PW",
    "PY",
    "QA",
    "RO",
    "RS",
    "RU",
    "RW",
    "SA",
    "SB",
    "SC",
    "SD",
    "SE",
    "SG",
    "SI",
    "SJ",
    "SK",
    "SL",
    "SN",
    "SO",
    "ST",
    "SV",
    "SX",
    "SY",
    "SZ",
    "TC",
    "TD",
    "TG",
    "TH",
    "TJ",
    "TK",
    "TL",
    "TM",
    "TN",
    "TO",
    "TR",
    "TV",
    "TZ",
    "UA",
    "UG",
    "US",
    "UY",
    "UZ",
    "VC",
    "VE",
    "VG",
    "VI",
    "VN",
    "VU",
    "WS",
    "YE",
    "ZA",
    "ZM",
    "ZW"
  ],
  "contributors": [
    {
      "id": 1,
      "name": "The Beatles",
      "link": "https://www.deezer.com/artist/1",
      "share": "https://www.deezer.com/artist/1?utm_source=deezer&utm_content=artist-1&utm_term=0_1746809963&utm_medium=web",
      "picture": "https://api.deezer.com/artist/1/image",
      "picture_small": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/56x56-000000-80-0-0.jpg",
      "picture_medium": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/250x250-000000-80-0-0.jpg",
      "picture_big": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/500x500-000000-80-0-0.jpg",
      "picture_xl": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/1000x1000-000000-80-0-0.jpg",
      "radio": true,
      "tracklist": "https://api.deezer.com/artist/1/top?limit=50",
      "type": "artist",
      "role": "Main"
    }
  ],
  "md5_image": "fcf05300b7c17ec77a6d01028a4bef61",
  "track_token": "AAAAAWgeNGtoH02ryS0zbI6KnzQOMHVHlUqj12EZzirBkte_f4Ezzc_JIas1jcNAXUWAjRH5FxmBI3K-hS7I82Lf29zvN1dOQYzUTTDdr-yKe8pJP9aVYBnRTuaG19eKz33FGodZUajjm3l20ueMuPn6tJtbI5h125jOGGqNHo5s2Zg2bSr-pfzKj7_bHPqGiVvM_LZR--zEHX7U7O6dBzsL9FXloUzJ0dHp9RbWZRLEWOBQ6JC2QCUcy4gsdE2K6xJijfoc2Ph7RI7gD-Qzdyk8rprqdLI40t4PHcxxZJGVROJbAlFCCaoPLivg_nzpA9XedIsgis9cRE6HFMe0uN97QcGltEcpLRRGDeWJLwMAySxfOpJajD4t5nIqpu1k8bT3o5J6DwdMsLSUiw",
  "artist": {
    "id": 1,
    "name": "The Beatles",
    "link": "https://www.deezer.com/artist/1",
    "share": "https://www.deezer.com/artist/1?utm_source=deezer&utm_content=artist-1&utm_term=0_1746809963&utm_medium=web",
    "picture": "https://api.deezer.com/artist/1/image",
    "picture_small": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/56x56-000000-80-0-0.jpg",
    "picture_medium": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/250x250-000000-80-0-0.jpg",
    "picture_big": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/500x500-000000-80-0-0.jpg",
    "picture_xl": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/1000x1000-000000-80-0-0.jpg",
    "radio": true,
    "tracklist": "https://api.deezer.com/artist/1/top?limit=50",
    "type": "artist"
  },
  "album": {
    "id": 12047958,
    "title": "Let It Be (Remastered)",
    "link": "https://www.deezer.com/album/12047958",
    "cover": "https://api.deezer.com/album/12047958/image",
    "cover_small": "https://cdn-images.dzcdn.net/images/cover/fcf05300b7c17ec77a6d01028a4bef61/56x56-000000-80-0-0.jpg",
    "cover_medium": "https://cdn-images.dzcdn.net/images/cover/fcf05300b7c17ec77a6d01028a4bef61/250x250-000000-80-0-0.jpg",
    "cover_big": "https://cdn-images.dzcdn.net/images/cover/fcf05300b7c17ec77a6d01028a4bef61/500x500-000000-80-0-0.jpg",
    "cover_xl": "https://cdn-images.dzcdn.net/images/cover/fcf05300b7c17ec77a6d01028a4bef61/1000x1000-000000-80-0-0.jpg",
    "md5_image": "fcf05300b7c17ec77a6d01028a4bef61",
    "release_date": "1970-05-08",
    "tracklist": "https://api.deezer.com/album/12047958/tracks",
    "type": "album"
  },
  "type": "track"
}









---------------------------------
Here's how to use search:
const data = null;

const xhr = new XMLHttpRequest();
xhr.withCredentials = true;

xhr.addEventListener('readystatechange', function () {
	if (this.readyState === this.DONE) {
		console.log(this.responseText);
	}
});

xhr.open('GET', 'https://deezerdevs-deezer.p.rapidapi.com/search?q=eminem');
xhr.setRequestHeader('x-rapidapi-key', '**************************************************');
xhr.setRequestHeader('x-rapidapi-host', 'deezerdevs-deezer.p.rapidapi.com');

xhr.send(data);

-----------------------------
And an example response from search I searched for "beatles let it be"

{
  "data": [
    {
      "id": 116348656,
      "readable": true,
      "title": "Let It Be (Remastered 2009)",
      "title_short": "Let It Be",
      "title_version": "(Remastered 2009)",
      "link": "https://www.deezer.com/track/116348656",
      "duration": 243,
      "rank": 892118,
      "explicit_lyrics": false,
      "explicit_content_lyrics": 0,
      "explicit_content_cover": 0,
      "preview": "https://cdnt-preview.dzcdn.net/api/1/1/a/f/5/0/af5b351b100513b1c167e5ed96b7ece3.mp3?hdnea=exp=1746810790~acl=/api/1/1/a/f/5/0/af5b351b100513b1c167e5ed96b7ece3.mp3*~data=user_id=0,application_id=42~hmac=b57fa147ada9272d8c119e15e2a03c8e64e7379285903de6bbff05a01a245ed9",
      "md5_image": "fcf05300b7c17ec77a6d01028a4bef61",
      "artist": {
        "id": 1,
        "name": "The Beatles",
        "link": "https://www.deezer.com/artist/1",
        "picture": "https://api.deezer.com/artist/1/image",
        "picture_small": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/56x56-000000-80-0-0.jpg",
        "picture_medium": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/250x250-000000-80-0-0.jpg",
        "picture_big": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/500x500-000000-80-0-0.jpg",
        "picture_xl": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/1000x1000-000000-80-0-0.jpg",
        "tracklist": "https://api.deezer.com/artist/1/top?limit=50",
        "type": "artist"
      },
      "album": {
        "id": 12047958,
        "title": "Let It Be (Remastered)",
        "cover": "https://api.deezer.com/album/12047958/image",
        "cover_small": "https://cdn-images.dzcdn.net/images/cover/fcf05300b7c17ec77a6d01028a4bef61/56x56-000000-80-0-0.jpg",
        "cover_medium": "https://cdn-images.dzcdn.net/images/cover/fcf05300b7c17ec77a6d01028a4bef61/250x250-000000-80-0-0.jpg",
        "cover_big": "https://cdn-images.dzcdn.net/images/cover/fcf05300b7c17ec77a6d01028a4bef61/500x500-000000-80-0-0.jpg",
        "cover_xl": "https://cdn-images.dzcdn.net/images/cover/fcf05300b7c17ec77a6d01028a4bef61/1000x1000-000000-80-0-0.jpg",
        "md5_image": "fcf05300b7c17ec77a6d01028a4bef61",
        "tracklist": "https://api.deezer.com/album/12047958/tracks",
        "type": "album"
      },
      "type": "track"
    },
    {
      "id": 116348642,
      "readable": true,
      "title": "Let It Be (Remastered 2015)",
      "title_short": "Let It Be",
      "title_version": "(Remastered 2015)",
      "link": "https://www.deezer.com/track/116348642",
      "duration": 230,
      "rank": 587464,
      "explicit_lyrics": false,
      "explicit_content_lyrics": 0,
      "explicit_content_cover": 0,
      "preview": "https://cdnt-preview.dzcdn.net/api/1/1/4/7/b/0/47bedf802597b55e39143364a78bd296.mp3?hdnea=exp=1746810790~acl=/api/1/1/4/7/b/0/47bedf802597b55e39143364a78bd296.mp3*~data=user_id=0,application_id=42~hmac=0522b800168a0ee7280c9cf449b291a92ea4fa7951b4fd73084d4cf5fa93f092",
      "md5_image": "c65b3bd84e81056e060be144381c06c8",
      "artist": {
        "id": 1,
        "name": "The Beatles",
        "link": "https://www.deezer.com/artist/1",
        "picture": "https://api.deezer.com/artist/1/image",
        "picture_small": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/56x56-000000-80-0-0.jpg",
        "picture_medium": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/250x250-000000-80-0-0.jpg",
        "picture_big": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/500x500-000000-80-0-0.jpg",
        "picture_xl": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/1000x1000-000000-80-0-0.jpg",
        "tracklist": "https://api.deezer.com/artist/1/top?limit=50",
        "type": "artist"
      },
      "album": {
        "id": 12047956,
        "title": "1 (Remastered)",
        "cover": "https://api.deezer.com/album/12047956/image",
        "cover_small": "https://cdn-images.dzcdn.net/images/cover/c65b3bd84e81056e060be144381c06c8/56x56-000000-80-0-0.jpg",
        "cover_medium": "https://cdn-images.dzcdn.net/images/cover/c65b3bd84e81056e060be144381c06c8/250x250-000000-80-0-0.jpg",
        "cover_big": "https://cdn-images.dzcdn.net/images/cover/c65b3bd84e81056e060be144381c06c8/500x500-000000-80-0-0.jpg",
        "cover_xl": "https://cdn-images.dzcdn.net/images/cover/c65b3bd84e81056e060be144381c06c8/1000x1000-000000-80-0-0.jpg",
        "md5_image": "c65b3bd84e81056e060be144381c06c8",
        "tracklist": "https://api.deezer.com/album/12047956/tracks",
        "type": "album"
      },
      "type": "track"
    },
    {
      "id": 116348134,
      "readable": true,
      "title": "Let It Be (Remastered 2009)",
      "title_short": "Let It Be",
      "title_version": "(Remastered 2009)",
      "link": "https://www.deezer.com/track/116348134",
      "duration": 230,
      "rank": 373145,
      "explicit_lyrics": false,
      "explicit_content_lyrics": 0,
      "explicit_content_cover": 0,
      "preview": "https://cdnt-preview.dzcdn.net/api/1/1/c/0/3/0/c03eab76988ceb9a354b51d9d0839d89.mp3?hdnea=exp=1746810790~acl=/api/1/1/c/0/3/0/c03eab76988ceb9a354b51d9d0839d89.mp3*~data=user_id=0,application_id=42~hmac=f2ce29e136778de46a617edeb0c1f9a071df11d659793962fb90fd02d9b4f27f",
      "md5_image": "32b6b5174e633cd6d182d00024dddcb5",
      "artist": {
        "id": 1,
        "name": "The Beatles",
        "link": "https://www.deezer.com/artist/1",
        "picture": "https://api.deezer.com/artist/1/image",
        "picture_small": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/56x56-000000-80-0-0.jpg",
        "picture_medium": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/250x250-000000-80-0-0.jpg",
        "picture_big": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/500x500-000000-80-0-0.jpg",
        "picture_xl": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/1000x1000-000000-80-0-0.jpg",
        "tracklist": "https://api.deezer.com/artist/1/top?limit=50",
        "type": "artist"
      },
      "album": {
        "id": 12047930,
        "title": "The Beatles 1967 - 1970 (Remastered)",
        "cover": "https://api.deezer.com/album/12047930/image",
        "cover_small": "https://cdn-images.dzcdn.net/images/cover/32b6b5174e633cd6d182d00024dddcb5/56x56-000000-80-0-0.jpg",
        "cover_medium": "https://cdn-images.dzcdn.net/images/cover/32b6b5174e633cd6d182d00024dddcb5/250x250-000000-80-0-0.jpg",
        "cover_big": "https://cdn-images.dzcdn.net/images/cover/32b6b5174e633cd6d182d00024dddcb5/500x500-000000-80-0-0.jpg",
        "cover_xl": "https://cdn-images.dzcdn.net/images/cover/32b6b5174e633cd6d182d00024dddcb5/1000x1000-000000-80-0-0.jpg",
        "md5_image": "32b6b5174e633cd6d182d00024dddcb5",
        "tracklist": "https://api.deezer.com/album/12047930/tracks",
        "type": "album"
      },
      "type": "track"
    },
    {
      "id": 1516275322,
      "readable": true,
      "title": "Let It Be (2021 Mix)",
      "title_short": "Let It Be",
      "title_version": "(2021 Mix)",
      "link": "https://www.deezer.com/track/1516275322",
      "duration": 243,
      "rank": 312765,
      "explicit_lyrics": false,
      "explicit_content_lyrics": 0,
      "explicit_content_cover": 0,
      "preview": "https://cdnt-preview.dzcdn.net/api/1/1/a/5/b/0/a5b8b10cece529ff6e8c20027c0e5c83.mp3?hdnea=exp=1746810790~acl=/api/1/1/a/5/b/0/a5b8b10cece529ff6e8c20027c0e5c83.mp3*~data=user_id=0,application_id=42~hmac=7145c12b0e4553f1f6e96a167bd68cdb9f27953d458862fcdb529e8fccdf9a29",
      "md5_image": "2acdd7fbc5f3f36f415c8ebe9d8c20cd",
      "artist": {
        "id": 1,
        "name": "The Beatles",
        "link": "https://www.deezer.com/artist/1",
        "picture": "https://api.deezer.com/artist/1/image",
        "picture_small": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/56x56-000000-80-0-0.jpg",
        "picture_medium": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/250x250-000000-80-0-0.jpg",
        "picture_big": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/500x500-000000-80-0-0.jpg",
        "picture_xl": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/1000x1000-000000-80-0-0.jpg",
        "tracklist": "https://api.deezer.com/artist/1/top?limit=50",
        "type": "artist"
      },
      "album": {
        "id": 264255742,
        "title": "Let It Be (Super Deluxe)",
        "cover": "https://api.deezer.com/album/264255742/image",
        "cover_small": "https://cdn-images.dzcdn.net/images/cover/2acdd7fbc5f3f36f415c8ebe9d8c20cd/56x56-000000-80-0-0.jpg",
        "cover_medium": "https://cdn-images.dzcdn.net/images/cover/2acdd7fbc5f3f36f415c8ebe9d8c20cd/250x250-000000-80-0-0.jpg",
        "cover_big": "https://cdn-images.dzcdn.net/images/cover/2acdd7fbc5f3f36f415c8ebe9d8c20cd/500x500-000000-80-0-0.jpg",
        "cover_xl": "https://cdn-images.dzcdn.net/images/cover/2acdd7fbc5f3f36f415c8ebe9d8c20cd/1000x1000-000000-80-0-0.jpg",
        "md5_image": "2acdd7fbc5f3f36f415c8ebe9d8c20cd",
        "tracklist": "https://api.deezer.com/album/264255742/tracks",
        "type": "album"
      },
      "type": "track"
    },
    {
      "id": 475334292,
      "readable": true,
      "title": "Let It Be (Naked Version / Remastered 2013)",
      "title_short": "Let It Be",
      "title_version": "(Naked Version / Remastered 2013)",
      "link": "https://www.deezer.com/track/475334292",
      "duration": 234,
      "rank": 347436,
      "explicit_lyrics": false,
      "explicit_content_lyrics": 0,
      "explicit_content_cover": 0,
      "preview": "https://cdnt-preview.dzcdn.net/api/1/1/7/7/f/0/77f86aeda4ff151d01cc701cf6a1e3c5.mp3?hdnea=exp=1746810790~acl=/api/1/1/7/7/f/0/77f86aeda4ff151d01cc701cf6a1e3c5.mp3*~data=user_id=0,application_id=42~hmac=53045a36c04009cce15b4308cc5fee73d6009569fd9f6da997847ac9ce2458a6",
      "md5_image": "efacd27f64a06aa8dae8de0dea7f0ac4",
      "artist": {
        "id": 1,
        "name": "The Beatles",
        "link": "https://www.deezer.com/artist/1",
        "picture": "https://api.deezer.com/artist/1/image",
        "picture_small": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/56x56-000000-80-0-0.jpg",
        "picture_medium": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/250x250-000000-80-0-0.jpg",
        "picture_big": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/500x500-000000-80-0-0.jpg",
        "picture_xl": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/1000x1000-000000-80-0-0.jpg",
        "tracklist": "https://api.deezer.com/artist/1/top?limit=50",
        "type": "artist"
      },
      "album": {
        "id": 59393282,
        "title": "Let It Be... Naked (Remastered)",
        "cover": "https://api.deezer.com/album/59393282/image",
        "cover_small": "https://cdn-images.dzcdn.net/images/cover/efacd27f64a06aa8dae8de0dea7f0ac4/56x56-000000-80-0-0.jpg",
        "cover_medium": "https://cdn-images.dzcdn.net/images/cover/efacd27f64a06aa8dae8de0dea7f0ac4/250x250-000000-80-0-0.jpg",
        "cover_big": "https://cdn-images.dzcdn.net/images/cover/efacd27f64a06aa8dae8de0dea7f0ac4/500x500-000000-80-0-0.jpg",
        "cover_xl": "https://cdn-images.dzcdn.net/images/cover/efacd27f64a06aa8dae8de0dea7f0ac4/1000x1000-000000-80-0-0.jpg",
        "md5_image": "efacd27f64a06aa8dae8de0dea7f0ac4",
        "tracklist": "https://api.deezer.com/album/59393282/tracks",
        "type": "album"
      },
      "type": "track"
    },
    {
      "id": 116348108,
      "readable": true,
      "title": "Hey Jude (Remastered 2009)",
      "title_short": "Hey Jude",
      "title_version": "(Remastered 2009)",
      "link": "https://www.deezer.com/track/116348108",
      "duration": 429,
      "rank": 634902,
      "explicit_lyrics": false,
      "explicit_content_lyrics": 0,
      "explicit_content_cover": 0,
      "preview": "https://cdnt-preview.dzcdn.net/api/1/1/3/8/6/0/386d7e440dc88dea8ac2c10e985ea401.mp3?hdnea=exp=1746810790~acl=/api/1/1/3/8/6/0/386d7e440dc88dea8ac2c10e985ea401.mp3*~data=user_id=0,application_id=42~hmac=ae16386a6d85004b934c44ea5a493e3f1fa244a3712ea92b2d8d9742b914a2e5",
      "md5_image": "32b6b5174e633cd6d182d00024dddcb5",
      "artist": {
        "id": 1,
        "name": "The Beatles",
        "link": "https://www.deezer.com/artist/1",
        "picture": "https://api.deezer.com/artist/1/image",
        "picture_small": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/56x56-000000-80-0-0.jpg",
        "picture_medium": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/250x250-000000-80-0-0.jpg",
        "picture_big": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/500x500-000000-80-0-0.jpg",
        "picture_xl": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/1000x1000-000000-80-0-0.jpg",
        "tracklist": "https://api.deezer.com/artist/1/top?limit=50",
        "type": "artist"
      },
      "album": {
        "id": 12047930,
        "title": "The Beatles 1967 - 1970 (Remastered)",
        "cover": "https://api.deezer.com/album/12047930/image",
        "cover_small": "https://cdn-images.dzcdn.net/images/cover/32b6b5174e633cd6d182d00024dddcb5/56x56-000000-80-0-0.jpg",
        "cover_medium": "https://cdn-images.dzcdn.net/images/cover/32b6b5174e633cd6d182d00024dddcb5/250x250-000000-80-0-0.jpg",
        "cover_big": "https://cdn-images.dzcdn.net/images/cover/32b6b5174e633cd6d182d00024dddcb5/500x500-000000-80-0-0.jpg",
        "cover_xl": "https://cdn-images.dzcdn.net/images/cover/32b6b5174e633cd6d182d00024dddcb5/1000x1000-000000-80-0-0.jpg",
        "md5_image": "32b6b5174e633cd6d182d00024dddcb5",
        "tracklist": "https://api.deezer.com/album/12047930/tracks",
        "type": "album"
      },
      "type": "track"
    },
    {
      "id": 1516275442,
      "readable": true,
      "title": "Let It Be / Please Please Me / Let It Be (Take 10)",
      "title_short": "Let It Be / Please Please Me / Let It Be",
      "title_version": "(Take 10)",
      "link": "https://www.deezer.com/track/1516275442",
      "duration": 272,
      "rank": 309764,
      "explicit_lyrics": false,
      "explicit_content_lyrics": 0,
      "explicit_content_cover": 0,
      "preview": "https://cdnt-preview.dzcdn.net/api/1/1/1/0/8/0/10801c2d9640f6690c4dabd91ddd213b.mp3?hdnea=exp=1746810790~acl=/api/1/1/1/0/8/0/10801c2d9640f6690c4dabd91ddd213b.mp3*~data=user_id=0,application_id=42~hmac=0031fde6766f2105f02a540a50467a0053b522efac54deaabfb2f028c00b1c06",
      "md5_image": "2acdd7fbc5f3f36f415c8ebe9d8c20cd",
      "artist": {
        "id": 1,
        "name": "The Beatles",
        "link": "https://www.deezer.com/artist/1",
        "picture": "https://api.deezer.com/artist/1/image",
        "picture_small": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/56x56-000000-80-0-0.jpg",
        "picture_medium": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/250x250-000000-80-0-0.jpg",
        "picture_big": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/500x500-000000-80-0-0.jpg",
        "picture_xl": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/1000x1000-000000-80-0-0.jpg",
        "tracklist": "https://api.deezer.com/artist/1/top?limit=50",
        "type": "artist"
      },
      "album": {
        "id": 264255742,
        "title": "Let It Be (Super Deluxe)",
        "cover": "https://api.deezer.com/album/264255742/image",
        "cover_small": "https://cdn-images.dzcdn.net/images/cover/2acdd7fbc5f3f36f415c8ebe9d8c20cd/56x56-000000-80-0-0.jpg",
        "cover_medium": "https://cdn-images.dzcdn.net/images/cover/2acdd7fbc5f3f36f415c8ebe9d8c20cd/250x250-000000-80-0-0.jpg",
        "cover_big": "https://cdn-images.dzcdn.net/images/cover/2acdd7fbc5f3f36f415c8ebe9d8c20cd/500x500-000000-80-0-0.jpg",
        "cover_xl": "https://cdn-images.dzcdn.net/images/cover/2acdd7fbc5f3f36f415c8ebe9d8c20cd/1000x1000-000000-80-0-0.jpg",
        "md5_image": "2acdd7fbc5f3f36f415c8ebe9d8c20cd",
        "tracklist": "https://api.deezer.com/album/264255742/tracks",
        "type": "album"
      },
      "type": "track"
    },
    {
      "id": 1516275652,
      "readable": true,
      "title": "Let It Be (Take 28)",
      "title_short": "Let It Be",
      "title_version": "(Take 28)",
      "link": "https://www.deezer.com/track/1516275652",
      "duration": 282,
      "rank": 153954,
      "explicit_lyrics": false,
      "explicit_content_lyrics": 0,
      "explicit_content_cover": 0,
      "preview": "https://cdnt-preview.dzcdn.net/api/1/1/5/8/a/0/58ae58f13468502b5969128cb9ecd2a1.mp3?hdnea=exp=1746810790~acl=/api/1/1/5/8/a/0/58ae58f13468502b5969128cb9ecd2a1.mp3*~data=user_id=0,application_id=42~hmac=c7d8635f3b5612fc3c4f551a90d2a556c07ab39054d9c0fefd8ff77a2924c510",
      "md5_image": "2acdd7fbc5f3f36f415c8ebe9d8c20cd",
      "artist": {
        "id": 1,
        "name": "The Beatles",
        "link": "https://www.deezer.com/artist/1",
        "picture": "https://api.deezer.com/artist/1/image",
        "picture_small": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/56x56-000000-80-0-0.jpg",
        "picture_medium": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/250x250-000000-80-0-0.jpg",
        "picture_big": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/500x500-000000-80-0-0.jpg",
        "picture_xl": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/1000x1000-000000-80-0-0.jpg",
        "tracklist": "https://api.deezer.com/artist/1/top?limit=50",
        "type": "artist"
      },
      "album": {
        "id": 264255742,
        "title": "Let It Be (Super Deluxe)",
        "cover": "https://api.deezer.com/album/264255742/image",
        "cover_small": "https://cdn-images.dzcdn.net/images/cover/2acdd7fbc5f3f36f415c8ebe9d8c20cd/56x56-000000-80-0-0.jpg",
        "cover_medium": "https://cdn-images.dzcdn.net/images/cover/2acdd7fbc5f3f36f415c8ebe9d8c20cd/250x250-000000-80-0-0.jpg",
        "cover_big": "https://cdn-images.dzcdn.net/images/cover/2acdd7fbc5f3f36f415c8ebe9d8c20cd/500x500-000000-80-0-0.jpg",
        "cover_xl": "https://cdn-images.dzcdn.net/images/cover/2acdd7fbc5f3f36f415c8ebe9d8c20cd/1000x1000-000000-80-0-0.jpg",
        "md5_image": "2acdd7fbc5f3f36f415c8ebe9d8c20cd",
        "tracklist": "https://api.deezer.com/album/264255742/tracks",
        "type": "album"
      },
      "type": "track"
    },
    {
      "id": 122172072,
      "readable": true,
      "title": "Let It Be (Anthology 3 Version)",
      "title_short": "Let It Be",
      "title_version": "(Anthology 3 Version)",
      "link": "https://www.deezer.com/track/122172072",
      "duration": 242,
      "rank": 149672,
      "explicit_lyrics": false,
      "explicit_content_lyrics": 0,
      "explicit_content_cover": 0,
      "preview": "https://cdnt-preview.dzcdn.net/api/1/1/3/b/7/0/3b71e871ec22bbfab0b27c4e8794be5c.mp3?hdnea=exp=1746810790~acl=/api/1/1/3/b/7/0/3b71e871ec22bbfab0b27c4e8794be5c.mp3*~data=user_id=0,application_id=42~hmac=3be7c899ed7826df969866981edc0a89fa8fe2aa27c444dfa0b6517d08b18c6a",
      "md5_image": "be01f37a129aa4c004d4bb755d6caa92",
      "artist": {
        "id": 1,
        "name": "The Beatles",
        "link": "https://www.deezer.com/artist/1",
        "picture": "https://api.deezer.com/artist/1/image",
        "picture_small": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/56x56-000000-80-0-0.jpg",
        "picture_medium": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/250x250-000000-80-0-0.jpg",
        "picture_big": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/500x500-000000-80-0-0.jpg",
        "picture_xl": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/1000x1000-000000-80-0-0.jpg",
        "tracklist": "https://api.deezer.com/artist/1/top?limit=50",
        "type": "artist"
      },
      "album": {
        "id": 12779758,
        "title": "Anthology 3",
        "cover": "https://api.deezer.com/album/12779758/image",
        "cover_small": "https://cdn-images.dzcdn.net/images/cover/be01f37a129aa4c004d4bb755d6caa92/56x56-000000-80-0-0.jpg",
        "cover_medium": "https://cdn-images.dzcdn.net/images/cover/be01f37a129aa4c004d4bb755d6caa92/250x250-000000-80-0-0.jpg",
        "cover_big": "https://cdn-images.dzcdn.net/images/cover/be01f37a129aa4c004d4bb755d6caa92/500x500-000000-80-0-0.jpg",
        "cover_xl": "https://cdn-images.dzcdn.net/images/cover/be01f37a129aa4c004d4bb755d6caa92/1000x1000-000000-80-0-0.jpg",
        "md5_image": "be01f37a129aa4c004d4bb755d6caa92",
        "tracklist": "https://api.deezer.com/album/12779758/tracks",
        "type": "album"
      },
      "type": "track"
    },
    {
      "id": 1516275772,
      "readable": true,
      "title": "Let It Be (1969 Glyn Johns Mix)",
      "title_short": "Let It Be",
      "title_version": "(1969 Glyn Johns Mix)",
      "link": "https://www.deezer.com/track/1516275772",
      "duration": 250,
      "rank": 172600,
      "explicit_lyrics": false,
      "explicit_content_lyrics": 0,
      "explicit_content_cover": 0,
      "preview": "https://cdnt-preview.dzcdn.net/api/1/1/b/4/9/0/b49bf60a4745218d40838865c299ecf0.mp3?hdnea=exp=1746810790~acl=/api/1/1/b/4/9/0/b49bf60a4745218d40838865c299ecf0.mp3*~data=user_id=0,application_id=42~hmac=e682736d6739f449bde7528b0a5159ed874a5fe2c1979b5a7320f101a5f998cc",
      "md5_image": "2acdd7fbc5f3f36f415c8ebe9d8c20cd",
      "artist": {
        "id": 1,
        "name": "The Beatles",
        "link": "https://www.deezer.com/artist/1",
        "picture": "https://api.deezer.com/artist/1/image",
        "picture_small": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/56x56-000000-80-0-0.jpg",
        "picture_medium": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/250x250-000000-80-0-0.jpg",
        "picture_big": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/500x500-000000-80-0-0.jpg",
        "picture_xl": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/1000x1000-000000-80-0-0.jpg",
        "tracklist": "https://api.deezer.com/artist/1/top?limit=50",
        "type": "artist"
      },
      "album": {
        "id": 264255742,
        "title": "Let It Be (Super Deluxe)",
        "cover": "https://api.deezer.com/album/264255742/image",
        "cover_small": "https://cdn-images.dzcdn.net/images/cover/2acdd7fbc5f3f36f415c8ebe9d8c20cd/56x56-000000-80-0-0.jpg",
        "cover_medium": "https://cdn-images.dzcdn.net/images/cover/2acdd7fbc5f3f36f415c8ebe9d8c20cd/250x250-000000-80-0-0.jpg",
        "cover_big": "https://cdn-images.dzcdn.net/images/cover/2acdd7fbc5f3f36f415c8ebe9d8c20cd/500x500-000000-80-0-0.jpg",
        "cover_xl": "https://cdn-images.dzcdn.net/images/cover/2acdd7fbc5f3f36f415c8ebe9d8c20cd/1000x1000-000000-80-0-0.jpg",
        "md5_image": "2acdd7fbc5f3f36f415c8ebe9d8c20cd",
        "tracklist": "https://api.deezer.com/album/264255742/tracks",
        "type": "album"
      },
      "type": "track"
    },
    {
      "id": 579901772,
      "readable": true,
      "title": "Let It Be (Unnumbered Rehearsal)",
      "title_short": "Let It Be",
      "title_version": "(Unnumbered Rehearsal)",
      "link": "https://www.deezer.com/track/579901772",
      "duration": 76,
      "rank": 168637,
      "explicit_lyrics": false,
      "explicit_content_lyrics": 0,
      "explicit_content_cover": 0,
      "preview": "https://cdnt-preview.dzcdn.net/api/1/1/0/8/9/0/0896488420768132afe05f12dd13772c.mp3?hdnea=exp=1746810790~acl=/api/1/1/0/8/9/0/0896488420768132afe05f12dd13772c.mp3*~data=user_id=0,application_id=42~hmac=3352e56795a62f8b203f902e5e984d376b8bca1a20bcee85faba094bed799fcb",
      "md5_image": "b8970772e00c2289e3aecef089589dbf",
      "artist": {
        "id": 1,
        "name": "The Beatles",
        "link": "https://www.deezer.com/artist/1",
        "picture": "https://api.deezer.com/artist/1/image",
        "picture_small": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/56x56-000000-80-0-0.jpg",
        "picture_medium": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/250x250-000000-80-0-0.jpg",
        "picture_big": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/500x500-000000-80-0-0.jpg",
        "picture_xl": "https://cdn-images.dzcdn.net/images/artist/fe9eb4463ea87452e84ed97e0c86b878/1000x1000-000000-80-0-0.jpg",
        "tracklist": "https://api.deezer.com/artist/1/top?limit=50",
        "type": "artist"
      },
      "album": {
        "id": 77649352,
        "title": "The Beatles (White Album / Super Deluxe)",
        "cover": "https://api.deezer.com/album/77649352/image",
        "cover_small": "https://cdn-images.dzcdn.net/images/cover/b8970772e00c2289e3aecef089589dbf/56x56-000000-80-0-0.jpg",
        "cover_medium": "https://cdn-images.dzcdn.net/images/cover/b8970772e00c2289e3aecef089589dbf/250x250-000000-80-0-0.jpg",
        "cover_big": "https://cdn-images.dzcdn.net/images/cover/b8970772e00c2289e3aecef089589dbf/500x500-000000-80-0-0.jpg",
        "cover_xl": "https://cdn-images.dzcdn.net/images/cover/b8970772e00c2289e3aecef089589dbf/1000x1000-000000-80-0-0.jpg",
        "md5_image": "b8970772e00c2289e3aecef089589dbf",
        "tracklist": "https://api.deezer.com/album/77649352/tracks",
        "type": "album"
      },
      "type": "track"
    },
    {
      "id": 14209971,
      "readable": true,
      "title": "Let It Be",
      "title_short": "Let It Be",
      "title_version": "",
      "link": "https://www.deezer.com/track/14209971",
      "duration": 230,
      "rank": 410039,
      "explicit_lyrics": false,
      "explicit_content_lyrics": 0,
      "explicit_content_cover": 2,
      "preview": "https://cdnt-preview.dzcdn.net/api/1/1/8/2/0/0/820100ffac0c6a09f1aff44f52653e4a.mp3?hdnea=exp=1746810790~acl=/api/1/1/8/2/0/0/820100ffac0c6a09f1aff44f52653e4a.mp3*~data=user_id=0,application_id=42~hmac=37a95a07716e484f075ec27ca4582fd108faec978596aa9ac65dcc9b006b7793",
      "md5_image": "070bd85275bfff22ce8338df5ee3b672",
      "artist": {
        "id": 1416434,
        "name": "The Beatles Recovered Band",
        "link": "https://www.deezer.com/artist/1416434",
        "picture": "https://api.deezer.com/artist/1416434/image",
        "picture_small": "https://cdn-images.dzcdn.net/images/artist/d3d7016f28147206409c42180b06886e/56x56-000000-80-0-0.jpg",
        "picture_medium": "https://cdn-images.dzcdn.net/images/artist/d3d7016f28147206409c42180b06886e/250x250-000000-80-0-0.jpg",
        "picture_big": "https://cdn-images.dzcdn.net/images/artist/d3d7016f28147206409c42180b06886e/500x500-000000-80-0-0.jpg",
        "picture_xl": "https://cdn-images.dzcdn.net/images/artist/d3d7016f28147206409c42180b06886e/1000x1000-000000-80-0-0.jpg",
        "tracklist": "https://api.deezer.com/artist/1416434/top?limit=50",
        "type": "artist"
      },
      "album": {
        "id": 1303739,
        "title": "Beatles 20 No. 1 Hits!",
        "cover": "https://api.deezer.com/album/1303739/image",
        "cover_small": "https://cdn-images.dzcdn.net/images/cover/070bd85275bfff22ce8338df5ee3b672/56x56-000000-80-0-0.jpg",
        "cover_medium": "https://cdn-images.dzcdn.net/images/cover/070bd85275bfff22ce8338df5ee3b672/250x250-000000-80-0-0.jpg",
        "cover_big": "https://cdn-images.dzcdn.net/images/cover/070bd85275bfff22ce8338df5ee3b672/500x500-000000-80-0-0.jpg",
        "cover_xl": "https://cdn-images.dzcdn.net/images/cover/070bd85275bfff22ce8338df5ee3b672/1000x1000-000000-80-0-0.jpg",
        "md5_image": "070bd85275bfff22ce8338df5ee3b672",
        "tracklist": "https://api.deezer.com/album/1303739/tracks",
        "type": "album"
      },
      "type": "track"
    },
    {
      "id": 115224778,
      "readable": true,
      "title": "Let It Be (Of The Beatles - Spanish Guitar Version)",
      "title_short": "Let It Be (Of The Beatles - Spanish Guitar Version)",
      "link": "https://www.deezer.com/track/115224778",
      "duration": 238,
      "rank": 67853,
      "explicit_lyrics": false,
      "explicit_content_lyrics": 0,
      "explicit_content_cover": 2,
      "preview": "https://cdnt-preview.dzcdn.net/api/1/1/e/6/b/0/e6bb42cbfbacbafad971cbc849942065.mp3?hdnea=exp=1746810790~acl=/api/1/1/e/6/b/0/e6bb42cbfbacbafad971cbc849942065.mp3*~data=user_id=0,application_id=42~hmac=1bdc70299da361de499474246db2e5315cb8c0a117538b0c756cd85773709efe",
      "md5_image": "95359867f5c3a00f50f3f39cb9f15627",
      "artist": {
        "id": 606926,
        "name": "The Spanish Guitar",
        "link": "https://www.deezer.com/artist/606926",
        "picture": "https://api.deezer.com/artist/606926/image",
        "picture_small": "https://cdn-images.dzcdn.net/images/artist/0f43d2b69a0d8b500bff350b719c1529/56x56-000000-80-0-0.jpg",
        "picture_medium": "https://cdn-images.dzcdn.net/images/artist/0f43d2b69a0d8b500bff350b719c1529/250x250-000000-80-0-0.jpg",
        "picture_big": "https://cdn-images.dzcdn.net/images/artist/0f43d2b69a0d8b500bff350b719c1529/500x500-000000-80-0-0.jpg",
        "picture_xl": "https://cdn-images.dzcdn.net/images/artist/0f43d2b69a0d8b500bff350b719c1529/1000x1000-000000-80-0-0.jpg",
        "tracklist": "https://api.deezer.com/artist/606926/top?limit=50",
        "type": "artist"
      },
      "album": {
        "id": 11940048,
        "title": "The Spanish Guitar Play Beatles \"Songs For Ever And Ever\"",
        "cover": "https://api.deezer.com/album/11940048/image",
        "cover_small": "https://cdn-images.dzcdn.net/images/cover/95359867f5c3a00f50f3f39cb9f15627/56x56-000000-80-0-0.jpg",
        "cover_medium": "https://cdn-images.dzcdn.net/images/cover/95359867f5c3a00f50f3f39cb9f15627/250x250-000000-80-0-0.jpg",
        "cover_big": "https://cdn-images.dzcdn.net/images/cover/95359867f5c3a00f50f3f39cb9f15627/500x500-000000-80-0-0.jpg",
        "cover_xl": "https://cdn-images.dzcdn.net/images/cover/95359867f5c3a00f50f3f39cb9f15627/1000x1000-000000-80-0-0.jpg",
        "md5_image": "95359867f5c3a00f50f3f39cb9f15627",
        "tracklist": "https://api.deezer.com/album/11940048/tracks",
        "type": "album"
      },
      "type": "track"
    },
    {
      "id": 872674302,
      "readable": true,
      "title": "Let It Be",
      "title_short": "Let It Be",
      "title_version": "",
      "link": "https://www.deezer.com/track/872674302",
      "duration": 257,
      "rank": 79277,
      "explicit_lyrics": false,
      "explicit_content_lyrics": 0,
      "explicit_content_cover": 2,
      "preview": "https://cdnt-preview.dzcdn.net/api/1/1/b/3/2/0/b32f679fcf1d051dec0c6321a84bff53.mp3?hdnea=exp=1746810790~acl=/api/1/1/b/3/2/0/b32f679fcf1d051dec0c6321a84bff53.mp3*~data=user_id=0,application_id=42~hmac=3b10ade5a089eb2ba51ed3b767fd145e849931bb503341eb1b7459ab8c7fa5f9",
      "md5_image": "af2ad4fea27574b6761c66c255806e83",
      "artist": {
        "id": 11386468,
        "name": "Blues Beatles",
        "link": "https://www.deezer.com/artist/11386468",
        "picture": "https://api.deezer.com/artist/11386468/image",
        "picture_small": "https://cdn-images.dzcdn.net/images/artist/c7216fff4cb7f77887451f1a7b8230a9/56x56-000000-80-0-0.jpg",
        "picture_medium": "https://cdn-images.dzcdn.net/images/artist/c7216fff4cb7f77887451f1a7b8230a9/250x250-000000-80-0-0.jpg",
        "picture_big": "https://cdn-images.dzcdn.net/images/artist/c7216fff4cb7f77887451f1a7b8230a9/500x500-000000-80-0-0.jpg",
        "picture_xl": "https://cdn-images.dzcdn.net/images/artist/c7216fff4cb7f77887451f1a7b8230a9/1000x1000-000000-80-0-0.jpg",
        "tracklist": "https://api.deezer.com/artist/11386468/top?limit=50",
        "type": "artist"
      },
      "album": {
        "id": 130933682,
        "title": "Let It Blues",
        "cover": "https://api.deezer.com/album/130933682/image",
        "cover_small": "https://cdn-images.dzcdn.net/images/cover/af2ad4fea27574b6761c66c255806e83/56x56-000000-80-0-0.jpg",
        "cover_medium": "https://cdn-images.dzcdn.net/images/cover/af2ad4fea27574b6761c66c255806e83/250x250-000000-80-0-0.jpg",
        "cover_big": "https://cdn-images.dzcdn.net/images/cover/af2ad4fea27574b6761c66c255806e83/500x500-000000-80-0-0.jpg",
        "cover_xl": "https://cdn-images.dzcdn.net/images/cover/af2ad4fea27574b6761c66c255806e83/1000x1000-000000-80-0-0.jpg",
        "md5_image": "af2ad4fea27574b6761c66c255806e83",
        "tracklist": "https://api.deezer.com/album/130933682/tracks",
        "type": "album"
      },
      "type": "track"
    },
    {
      "id": 7995835,
      "readable": true,
      "title": "Let It Be",
      "title_short": "Let It Be",
      "link": "https://www.deezer.com/track/7995835",
      "duration": 224,
      "rank": 91184,
      "explicit_lyrics": false,
      "explicit_content_lyrics": 0,
      "explicit_content_cover": 2,
      "preview": "https://cdnt-preview.dzcdn.net/api/1/1/5/d/3/0/5d35b023c14f3cf273adc439354c83f3.mp3?hdnea=exp=1746810790~acl=/api/1/1/5/d/3/0/5d35b023c14f3cf273adc439354c83f3.mp3*~data=user_id=0,application_id=42~hmac=638757764d017d192dc9a586a684a9c2bfa78216f34f91d71014b087473a1637",
      "md5_image": "adb8280bd33c295bb53ce014f4ce833a",
      "artist": {
        "id": 68382,
        "name": "London Symphony Orchestra",
        "link": "https://www.deezer.com/artist/68382",
        "picture": "https://api.deezer.com/artist/68382/image",
        "picture_small": "https://cdn-images.dzcdn.net/images/artist/b851ada2be988a5a7e1771104abf1f11/56x56-000000-80-0-0.jpg",
        "picture_medium": "https://cdn-images.dzcdn.net/images/artist/b851ada2be988a5a7e1771104abf1f11/250x250-000000-80-0-0.jpg",
        "picture_big": "https://cdn-images.dzcdn.net/images/artist/b851ada2be988a5a7e1771104abf1f11/500x500-000000-80-0-0.jpg",
        "picture_xl": "https://cdn-images.dzcdn.net/images/artist/b851ada2be988a5a7e1771104abf1f11/1000x1000-000000-80-0-0.jpg",
        "tracklist": "https://api.deezer.com/artist/68382/top?limit=50",
        "type": "artist"
      },
      "album": {
        "id": 737924,
        "title": "The Beatles Classics",
        "cover": "https://api.deezer.com/album/737924/image",
        "cover_small": "https://cdn-images.dzcdn.net/images/cover/adb8280bd33c295bb53ce014f4ce833a/56x56-000000-80-0-0.jpg",
        "cover_medium": "https://cdn-images.dzcdn.net/images/cover/adb8280bd33c295bb53ce014f4ce833a/250x250-000000-80-0-0.jpg",
        "cover_big": "https://cdn-images.dzcdn.net/images/cover/adb8280bd33c295bb53ce014f4ce833a/500x500-000000-80-0-0.jpg",
        "cover_xl": "https://cdn-images.dzcdn.net/images/cover/adb8280bd33c295bb53ce014f4ce833a/1000x1000-000000-80-0-0.jpg",
        "md5_image": "adb8280bd33c295bb53ce014f4ce833a",
        "tracklist": "https://api.deezer.com/album/737924/tracks",
        "type": "album"
      },
      "type": "track"
    },
    {
      "id": 37773371,
      "readable": true,
      "title": "Let It Be (In The Style Of The Beatles) [Instrumental Version]",
      "title_short": "Let It Be (In The Style Of The Beatles) [Instrumental Version]",
      "title_version": "",
      "link": "https://www.deezer.com/track/37773371",
      "duration": 226,
      "rank": 65023,
      "explicit_lyrics": false,
      "explicit_content_lyrics": 2,
      "explicit_content_cover": 2,
      "preview": "https://cdnt-preview.dzcdn.net/api/1/1/c/c/5/0/cc5c37a1afddfb0221c4554f03192001.mp3?hdnea=exp=1746810790~acl=/api/1/1/c/c/5/0/cc5c37a1afddfb0221c4554f03192001.mp3*~data=user_id=0,application_id=42~hmac=462a210b1ef34a270e6f8ad2ad2e97d6e1a495b724867dd61845f91978e277bb",
      "md5_image": "383bbf3fcc2049d83c39218317c780d0",
      "artist": {
        "id": 2678251,
        "name": "United Guitar Players",
        "link": "https://www.deezer.com/artist/2678251",
        "picture": "https://api.deezer.com/artist/2678251/image",
        "picture_small": "https://cdn-images.dzcdn.net/images/artist/85829387b618de7379cc9ee34cf27737/56x56-000000-80-0-0.jpg",
        "picture_medium": "https://cdn-images.dzcdn.net/images/artist/85829387b618de7379cc9ee34cf27737/250x250-000000-80-0-0.jpg",
        "picture_big": "https://cdn-images.dzcdn.net/images/artist/85829387b618de7379cc9ee34cf27737/500x500-000000-80-0-0.jpg",
        "picture_xl": "https://cdn-images.dzcdn.net/images/artist/85829387b618de7379cc9ee34cf27737/1000x1000-000000-80-0-0.jpg",
        "tracklist": "https://api.deezer.com/artist/2678251/top?limit=50",
        "type": "artist"
      },
      "album": {
        "id": 3672611,
        "title": "Popular Hits On Spanish Acoustic Guitar, Vol. 3",
        "cover": "https://api.deezer.com/album/3672611/image",
        "cover_small": "https://cdn-images.dzcdn.net/images/cover/383bbf3fcc2049d83c39218317c780d0/56x56-000000-80-0-0.jpg",
        "cover_medium": "https://cdn-images.dzcdn.net/images/cover/383bbf3fcc2049d83c39218317c780d0/250x250-000000-80-0-0.jpg",
        "cover_big": "https://cdn-images.dzcdn.net/images/cover/383bbf3fcc2049d83c39218317c780d0/500x500-000000-80-0-0.jpg",
        "cover_xl": "https://cdn-images.dzcdn.net/images/cover/383bbf3fcc2049d83c39218317c780d0/1000x1000-000000-80-0-0.jpg",
        "md5_image": "383bbf3fcc2049d83c39218317c780d0",
        "tracklist": "https://api.deezer.com/album/3672611/tracks",
        "type": "album"
      },
      "type": "track"
    },
    {
      "id": 12539993,
      "readable": true,
      "title": "Let It Be (Live)",
      "title_short": "Let It Be",
      "title_version": "(Live)",
      "link": "https://www.deezer.com/track/12539993",
      "duration": 242,
      "rank": 36079,
      "explicit_lyrics": false,
      "explicit_content_lyrics": 0,
      "explicit_content_cover": 2,
      "preview": "https://cdnt-preview.dzcdn.net/api/1/1/2/6/5/0/26571be2bb38eec23ac2446467978fa3.mp3?hdnea=exp=1746810790~acl=/api/1/1/2/6/5/0/26571be2bb38eec23ac2446467978fa3.mp3*~data=user_id=0,application_id=42~hmac=e6ea23b1ffb3c9dfcaf2c5741c319399be98e13a3ee86595ca3e546f21f6c7c6",
      "md5_image": "9f23b6d93c3365802159f9134e523d8a",
      "artist": {
        "id": 94217,
        "name": "The Beatles Revival Band",
        "link": "https://www.deezer.com/artist/94217",
        "picture": "https://api.deezer.com/artist/94217/image",
        "picture_small": "https://cdn-images.dzcdn.net/images/artist/95ba9d0a3d905f3a767c3df492ccaee6/56x56-000000-80-0-0.jpg",
        "picture_medium": "https://cdn-images.dzcdn.net/images/artist/95ba9d0a3d905f3a767c3df492ccaee6/250x250-000000-80-0-0.jpg",
        "picture_big": "https://cdn-images.dzcdn.net/images/artist/95ba9d0a3d905f3a767c3df492ccaee6/500x500-000000-80-0-0.jpg",
        "picture_xl": "https://cdn-images.dzcdn.net/images/artist/95ba9d0a3d905f3a767c3df492ccaee6/1000x1000-000000-80-0-0.jpg",
        "tracklist": "https://api.deezer.com/artist/94217/top?limit=50",
        "type": "artist"
      },
      "album": {
        "id": 1150509,
        "title": "Live At the Alte Oper Frankfurt",
        "cover": "https://api.deezer.com/album/1150509/image",
        "cover_small": "https://cdn-images.dzcdn.net/images/cover/9f23b6d93c3365802159f9134e523d8a/56x56-000000-80-0-0.jpg",
        "cover_medium": "https://cdn-images.dzcdn.net/images/cover/9f23b6d93c3365802159f9134e523d8a/250x250-000000-80-0-0.jpg",
        "cover_big": "https://cdn-images.dzcdn.net/images/cover/9f23b6d93c3365802159f9134e523d8a/500x500-000000-80-0-0.jpg",
        "cover_xl": "https://cdn-images.dzcdn.net/images/cover/9f23b6d93c3365802159f9134e523d8a/1000x1000-000000-80-0-0.jpg",
        "md5_image": "9f23b6d93c3365802159f9134e523d8a",
        "tracklist": "https://api.deezer.com/album/1150509/tracks",
        "type": "album"
      },
      "type": "track"
    },
    {
      "id": 1849334177,
      "readable": true,
      "title": "Let It Be (live)",
      "title_short": "Let It Be",
      "title_version": "(live)",
      "link": "https://www.deezer.com/track/1849334177",
      "duration": 217,
      "rank": 91919,
      "explicit_lyrics": false,
      "explicit_content_lyrics": 0,
      "explicit_content_cover": 2,
      "preview": "https://cdnt-preview.dzcdn.net/api/1/1/5/1/d/0/51df19ce886ed6475866a2d7e010e03b.mp3?hdnea=exp=1746810790~acl=/api/1/1/5/1/d/0/51df19ce886ed6475866a2d7e010e03b.mp3*~data=user_id=0,application_id=42~hmac=0386d49faeab0551b57bd109cea0f3eaeb9ab335139abfa1c80b0b1f35e28b35",
      "md5_image": "63e3fee1be41721326953b5e0c3f97ab",
      "artist": {
        "id": 94217,
        "name": "The Beatles Revival Band",
        "link": "https://www.deezer.com/artist/94217",
        "picture": "https://api.deezer.com/artist/94217/image",
        "picture_small": "https://cdn-images.dzcdn.net/images/artist/95ba9d0a3d905f3a767c3df492ccaee6/56x56-000000-80-0-0.jpg",
        "picture_medium": "https://cdn-images.dzcdn.net/images/artist/95ba9d0a3d905f3a767c3df492ccaee6/250x250-000000-80-0-0.jpg",
        "picture_big": "https://cdn-images.dzcdn.net/images/artist/95ba9d0a3d905f3a767c3df492ccaee6/500x500-000000-80-0-0.jpg",
        "picture_xl": "https://cdn-images.dzcdn.net/images/artist/95ba9d0a3d905f3a767c3df492ccaee6/1000x1000-000000-80-0-0.jpg",
        "tracklist": "https://api.deezer.com/artist/94217/top?limit=50",
        "type": "artist"
      },
      "album": {
        "id": 342241607,
        "title": "40th Anniversary (Live at the FABRIK in Hamburg)",
        "cover": "https://api.deezer.com/album/342241607/image",
        "cover_small": "https://cdn-images.dzcdn.net/images/cover/63e3fee1be41721326953b5e0c3f97ab/56x56-000000-80-0-0.jpg",
        "cover_medium": "https://cdn-images.dzcdn.net/images/cover/63e3fee1be41721326953b5e0c3f97ab/250x250-000000-80-0-0.jpg",
        "cover_big": "https://cdn-images.dzcdn.net/images/cover/63e3fee1be41721326953b5e0c3f97ab/500x500-000000-80-0-0.jpg",
        "cover_xl": "https://cdn-images.dzcdn.net/images/cover/63e3fee1be41721326953b5e0c3f97ab/1000x1000-000000-80-0-0.jpg",
        "md5_image": "63e3fee1be41721326953b5e0c3f97ab",
        "tracklist": "https://api.deezer.com/album/342241607/tracks",
        "type": "album"
      },
      "type": "track"
    },
    {
      "id": 12519841,
      "readable": true,
      "title": "Let It Be (Originally Performed By the Beatles)",
      "title_short": "Let It Be",
      "title_version": "(Originally Performed By the Beatles)",
      "link": "https://www.deezer.com/track/12519841",
      "duration": 250,
      "rank": 38369,
      "explicit_lyrics": false,
      "explicit_content_lyrics": 0,
      "explicit_content_cover": 2,
      "preview": "https://cdnt-preview.dzcdn.net/api/1/1/f/9/c/0/f9c88e300cbcad04b50402fa5b333c4a.mp3?hdnea=exp=1746810790~acl=/api/1/1/f/9/c/0/f9c88e300cbcad04b50402fa5b333c4a.mp3*~data=user_id=0,application_id=42~hmac=cc1b27633f9c5b929a1d6092a545e9fb7fa02609305e3f68ee0c7e5069abd771",
      "md5_image": "45b38fcb5f30b26e2fce3b4a0d7dc027",
      "artist": {
        "id": 1361171,
        "name": "The Ultimate Beatles Cover Band",
        "link": "https://www.deezer.com/artist/1361171",
        "picture": "https://api.deezer.com/artist/1361171/image",
        "picture_small": "https://cdn-images.dzcdn.net/images/artist/45b38fcb5f30b26e2fce3b4a0d7dc027/56x56-000000-80-0-0.jpg",
        "picture_medium": "https://cdn-images.dzcdn.net/images/artist/45b38fcb5f30b26e2fce3b4a0d7dc027/250x250-000000-80-0-0.jpg",
        "picture_big": "https://cdn-images.dzcdn.net/images/artist/45b38fcb5f30b26e2fce3b4a0d7dc027/500x500-000000-80-0-0.jpg",
        "picture_xl": "https://cdn-images.dzcdn.net/images/artist/45b38fcb5f30b26e2fce3b4a0d7dc027/1000x1000-000000-80-0-0.jpg",
        "tracklist": "https://api.deezer.com/artist/1361171/top?limit=50",
        "type": "artist"
      },
      "album": {
        "id": 1148832,
        "title": "Beatles Gold",
        "cover": "https://api.deezer.com/album/1148832/image",
        "cover_small": "https://cdn-images.dzcdn.net/images/cover/45b38fcb5f30b26e2fce3b4a0d7dc027/56x56-000000-80-0-0.jpg",
        "cover_medium": "https://cdn-images.dzcdn.net/images/cover/45b38fcb5f30b26e2fce3b4a0d7dc027/250x250-000000-80-0-0.jpg",
        "cover_big": "https://cdn-images.dzcdn.net/images/cover/45b38fcb5f30b26e2fce3b4a0d7dc027/500x500-000000-80-0-0.jpg",
        "cover_xl": "https://cdn-images.dzcdn.net/images/cover/45b38fcb5f30b26e2fce3b4a0d7dc027/1000x1000-000000-80-0-0.jpg",
        "md5_image": "45b38fcb5f30b26e2fce3b4a0d7dc027",
        "tracklist": "https://api.deezer.com/album/1148832/tracks",
        "type": "album"
      },
      "type": "track"
    },
    {
      "id": 2125302267,
      "readable": true,
      "title": "Let It Be",
      "title_short": "Let It Be",
      "title_version": "",
      "link": "https://www.deezer.com/track/2125302267",
      "duration": 168,
      "rank": 63753,
      "explicit_lyrics": false,
      "explicit_content_lyrics": 0,
      "explicit_content_cover": 2,
      "preview": "https://cdnt-preview.dzcdn.net/api/1/1/3/6/7/0/367217a9226cff4884e0a9d15f77e30a.mp3?hdnea=exp=1746810790~acl=/api/1/1/3/6/7/0/367217a9226cff4884e0a9d15f77e30a.mp3*~data=user_id=0,application_id=42~hmac=6d38e136b12e7ff2af790c961a880539398d9b605a9557d0713a30a6eb092617",
      "md5_image": "a441e25a743f52053a0c464049f0d6c3",
      "artist": {
        "id": 198980527,
        "name": "The Beatles Piano Covers",
        "link": "https://www.deezer.com/artist/198980527",
        "picture": "https://api.deezer.com/artist/198980527/image",
        "picture_small": "https://cdn-images.dzcdn.net/images/artist/a441e25a743f52053a0c464049f0d6c3/56x56-000000-80-0-0.jpg",
        "picture_medium": "https://cdn-images.dzcdn.net/images/artist/a441e25a743f52053a0c464049f0d6c3/250x250-000000-80-0-0.jpg",
        "picture_big": "https://cdn-images.dzcdn.net/images/artist/a441e25a743f52053a0c464049f0d6c3/500x500-000000-80-0-0.jpg",
        "picture_xl": "https://cdn-images.dzcdn.net/images/artist/a441e25a743f52053a0c464049f0d6c3/1000x1000-000000-80-0-0.jpg",
        "tracklist": "https://api.deezer.com/artist/198980527/top?limit=50",
        "type": "artist"
      },
      "album": {
        "id": 400842837,
        "title": "The Beatles Piano Covers",
        "cover": "https://api.deezer.com/album/400842837/image",
        "cover_small": "https://cdn-images.dzcdn.net/images/cover/a441e25a743f52053a0c464049f0d6c3/56x56-000000-80-0-0.jpg",
        "cover_medium": "https://cdn-images.dzcdn.net/images/cover/a441e25a743f52053a0c464049f0d6c3/250x250-000000-80-0-0.jpg",
        "cover_big": "https://cdn-images.dzcdn.net/images/cover/a441e25a743f52053a0c464049f0d6c3/500x500-000000-80-0-0.jpg",
        "cover_xl": "https://cdn-images.dzcdn.net/images/cover/a441e25a743f52053a0c464049f0d6c3/1000x1000-000000-80-0-0.jpg",
        "md5_image": "a441e25a743f52053a0c464049f0d6c3",
        "tracklist": "https://api.deezer.com/album/400842837/tracks",
        "type": "album"
      },
      "type": "track"
    },
    {
      "id": 97488408,
      "readable": true,
      "title": "Let It Be (Original)",
      "title_short": "Let It Be",
      "title_version": "(Original)",
      "link": "https://www.deezer.com/track/97488408",
      "duration": 229,
      "rank": 62259,
      "explicit_lyrics": false,
      "explicit_content_lyrics": 0,
      "explicit_content_cover": 2,
      "preview": "https://cdnt-preview.dzcdn.net/api/1/1/6/a/4/0/6a431f541e5b12cdf02a6a4a9c9bfff5.mp3?hdnea=exp=1746810790~acl=/api/1/1/6/a/4/0/6a431f541e5b12cdf02a6a4a9c9bfff5.mp3*~data=user_id=0,application_id=42~hmac=3ead6e8a370ea153c70dabd838a6923e48c1927ddbe3dd850ee7b36cecd15101",
      "md5_image": "bd4c57638e58e65d01386737cdadb367",
      "artist": {
        "id": 7701780,
        "name": "Backstage Beatles",
        "link": "https://www.deezer.com/artist/7701780",
        "picture": "https://api.deezer.com/artist/7701780/image",
        "picture_small": "https://cdn-images.dzcdn.net/images/artist/bd4c57638e58e65d01386737cdadb367/56x56-000000-80-0-0.jpg",
        "picture_medium": "https://cdn-images.dzcdn.net/images/artist/bd4c57638e58e65d01386737cdadb367/250x250-000000-80-0-0.jpg",
        "picture_big": "https://cdn-images.dzcdn.net/images/artist/bd4c57638e58e65d01386737cdadb367/500x500-000000-80-0-0.jpg",
        "picture_xl": "https://cdn-images.dzcdn.net/images/artist/bd4c57638e58e65d01386737cdadb367/1000x1000-000000-80-0-0.jpg",
        "tracklist": "https://api.deezer.com/artist/7701780/top?limit=50",
        "type": "artist"
      },
      "album": {
        "id": 9926868,
        "title": "The Backstage Beatles Play The Beatles Greatest Hits (Original)",
        "cover": "https://api.deezer.com/album/9926868/image",
        "cover_small": "https://cdn-images.dzcdn.net/images/cover/bd4c57638e58e65d01386737cdadb367/56x56-000000-80-0-0.jpg",
        "cover_medium": "https://cdn-images.dzcdn.net/images/cover/bd4c57638e58e65d01386737cdadb367/250x250-000000-80-0-0.jpg",
        "cover_big": "https://cdn-images.dzcdn.net/images/cover/bd4c57638e58e65d01386737cdadb367/500x500-000000-80-0-0.jpg",
        "cover_xl": "https://cdn-images.dzcdn.net/images/cover/bd4c57638e58e65d01386737cdadb367/1000x1000-000000-80-0-0.jpg",
        "md5_image": "bd4c57638e58e65d01386737cdadb367",
        "tracklist": "https://api.deezer.com/album/9926868/tracks",
        "type": "album"
      },
      "type": "track"
    },
    {
      "id": 4227804,
      "readable": true,
      "title": "Lennon / McCartney: Let It Be - from the \"Let It Be\" 1970 album by The Beatles",
      "title_short": "Lennon / McCartney: Let It Be - from the \"Let It Be\" 1970 album by The Beatles",
      "title_version": "",
      "link": "https://www.deezer.com/track/4227804",
      "duration": 330,
      "rank": 86710,
      "explicit_lyrics": false,
      "explicit_content_lyrics": 6,
      "explicit_content_cover": 2,
      "preview": "https://cdnt-preview.dzcdn.net/api/1/1/0/3/e/0/03ed291a1fc05e58e5fa3d34c5b4c2e7.mp3?hdnea=exp=1746810790~acl=/api/1/1/0/3/e/0/03ed291a1fc05e58e5fa3d34c5b4c2e7.mp3*~data=user_id=0,application_id=42~hmac=d14cf4a6e6f23c6c491089d643aef98f8cfdc546db0fce70942538a9948f62a3",
      "md5_image": "da8032ccad2d602750eb668d6231f98c",
      "artist": {
        "id": 71155,
        "name": "Lesley Garrett",
        "link": "https://www.deezer.com/artist/71155",
        "picture": "https://api.deezer.com/artist/71155/image",
        "picture_small": "https://cdn-images.dzcdn.net/images/artist/2c288a83b6e7f15927552e3d93e5ea8b/56x56-000000-80-0-0.jpg",
        "picture_medium": "https://cdn-images.dzcdn.net/images/artist/2c288a83b6e7f15927552e3d93e5ea8b/250x250-000000-80-0-0.jpg",
        "picture_big": "https://cdn-images.dzcdn.net/images/artist/2c288a83b6e7f15927552e3d93e5ea8b/500x500-000000-80-0-0.jpg",
        "picture_xl": "https://cdn-images.dzcdn.net/images/artist/2c288a83b6e7f15927552e3d93e5ea8b/1000x1000-000000-80-0-0.jpg",
        "tracklist": "https://api.deezer.com/artist/71155/top?limit=50",
        "type": "artist"
      },
      "album": {
        "id": 393629,
        "title": "The Very Best of Lesley Garrett",
        "cover": "https://api.deezer.com/album/393629/image",
        "cover_small": "https://cdn-images.dzcdn.net/images/cover/da8032ccad2d602750eb668d6231f98c/56x56-000000-80-0-0.jpg",
        "cover_medium": "https://cdn-images.dzcdn.net/images/cover/da8032ccad2d602750eb668d6231f98c/250x250-000000-80-0-0.jpg",
        "cover_big": "https://cdn-images.dzcdn.net/images/cover/da8032ccad2d602750eb668d6231f98c/500x500-000000-80-0-0.jpg",
        "cover_xl": "https://cdn-images.dzcdn.net/images/cover/da8032ccad2d602750eb668d6231f98c/1000x1000-000000-80-0-0.jpg",
        "md5_image": "da8032ccad2d602750eb668d6231f98c",
        "tracklist": "https://api.deezer.com/album/393629/tracks",
        "type": "album"
      },
      "type": "track"
    },
    {
      "id": 111003840,
      "readable": true,
      "title": "Medley The Beatles , Here comes the sun, When i'm 64, Let it be,Hey Jude,All my loving, And I love her, Yesterday,Michelle, Norweg",
      "title_short": "Medley The Beatles , Here comes the sun, When i'm 64, Let it be,Hey Jude,All my loving, And I love her, Yesterday,Michelle, Norweg",
      "title_version": "",
      "link": "https://www.deezer.com/track/111003840",
      "duration": 433,
      "rank": 26887,
      "explicit_lyrics": false,
      "explicit_content_lyrics": 0,
      "explicit_content_cover": 2,
      "preview": "https://cdnt-preview.dzcdn.net/api/1/1/9/a/9/0/9a918f07366ffbc4e94c8dad5cdd5b89.mp3?hdnea=exp=1746810790~acl=/api/1/1/9/a/9/0/9a918f07366ffbc4e94c8dad5cdd5b89.mp3*~data=user_id=0,application_id=42~hmac=d8c4cca000cb3497dcc3c1d7b26a0c381e2cd995abbbb510545787a3bcea42b5",
      "md5_image": "8136c5668aecba181b6bbd5b6f166ed6",
      "artist": {
        "id": 9156770,
        "name": "Anton van Doornmalen",
        "link": "https://www.deezer.com/artist/9156770",
        "picture": "https://api.deezer.com/artist/9156770/image",
        "picture_small": "https://cdn-images.dzcdn.net/images/artist/8136c5668aecba181b6bbd5b6f166ed6/56x56-000000-80-0-0.jpg",
        "picture_medium": "https://cdn-images.dzcdn.net/images/artist/8136c5668aecba181b6bbd5b6f166ed6/250x250-000000-80-0-0.jpg",
        "picture_big": "https://cdn-images.dzcdn.net/images/artist/8136c5668aecba181b6bbd5b6f166ed6/500x500-000000-80-0-0.jpg",
        "picture_xl": "https://cdn-images.dzcdn.net/images/artist/8136c5668aecba181b6bbd5b6f166ed6/1000x1000-000000-80-0-0.jpg",
        "tracklist": "https://api.deezer.com/artist/9156770/top?limit=50",
        "type": "artist"
      },
      "album": {
        "id": 11544162,
        "title": "Songs for the Asking",
        "cover": "https://api.deezer.com/album/11544162/image",
        "cover_small": "https://cdn-images.dzcdn.net/images/cover/8136c5668aecba181b6bbd5b6f166ed6/56x56-000000-80-0-0.jpg",
        "cover_medium": "https://cdn-images.dzcdn.net/images/cover/8136c5668aecba181b6bbd5b6f166ed6/250x250-000000-80-0-0.jpg",
        "cover_big": "https://cdn-images.dzcdn.net/images/cover/8136c5668aecba181b6bbd5b6f166ed6/500x500-000000-80-0-0.jpg",
        "cover_xl": "https://cdn-images.dzcdn.net/images/cover/8136c5668aecba181b6bbd5b6f166ed6/1000x1000-000000-80-0-0.jpg",
        "md5_image": "8136c5668aecba181b6bbd5b6f166ed6",
        "tracklist": "https://api.deezer.com/album/11544162/tracks",
        "type": "album"
      },
      "type": "track"
    },
    {
      "id": 15456746,
      "readable": true,
      "title": "Beatles Medley: Yesterday / Let It Be / Hey Jude / Yellow Submarine",
      "title_short": "Beatles Medley: Yesterday / Let It Be / Hey Jude / Yellow Submarine",
      "title_version": "",
      "link": "https://www.deezer.com/track/15456746",
      "duration": 376,
      "rank": 22023,
      "explicit_lyrics": false,
      "explicit_content_lyrics": 0,
      "explicit_content_cover": 2,
      "preview": "https://cdnt-preview.dzcdn.net/api/1/1/b/7/8/0/b78cb0c003ade25849ee8aef634c8339.mp3?hdnea=exp=1746810790~acl=/api/1/1/b/7/8/0/b78cb0c003ade25849ee8aef634c8339.mp3*~data=user_id=0,application_id=42~hmac=3e5799cc4fcd8a07719fcf3e4d3df4d1ddb522520bfbb5a6b0b1ad0a8564e28c",
      "md5_image": "6234a4d5e58e685debdcd8697b671cb0",
      "artist": {
        "id": 86457972,
        "name": "Tenor Sax",
        "link": "https://www.deezer.com/artist/86457972",
        "picture": "https://api.deezer.com/artist/86457972/image",
        "picture_small": "https://cdn-images.dzcdn.net/images/artist/6234a4d5e58e685debdcd8697b671cb0/56x56-000000-80-0-0.jpg",
        "picture_medium": "https://cdn-images.dzcdn.net/images/artist/6234a4d5e58e685debdcd8697b671cb0/250x250-000000-80-0-0.jpg",
        "picture_big": "https://cdn-images.dzcdn.net/images/artist/6234a4d5e58e685debdcd8697b671cb0/500x500-000000-80-0-0.jpg",
        "picture_xl": "https://cdn-images.dzcdn.net/images/artist/6234a4d5e58e685debdcd8697b671cb0/1000x1000-000000-80-0-0.jpg",
        "tracklist": "https://api.deezer.com/artist/86457972/top?limit=50",
        "type": "artist"
      },
      "album": {
        "id": 1428148,
        "title": "Romantic Sax",
        "cover": "https://api.deezer.com/album/1428148/image",
        "cover_small": "https://cdn-images.dzcdn.net/images/cover/6234a4d5e58e685debdcd8697b671cb0/56x56-000000-80-0-0.jpg",
        "cover_medium": "https://cdn-images.dzcdn.net/images/cover/6234a4d5e58e685debdcd8697b671cb0/250x250-000000-80-0-0.jpg",
        "cover_big": "https://cdn-images.dzcdn.net/images/cover/6234a4d5e58e685debdcd8697b671cb0/500x500-000000-80-0-0.jpg",
        "cover_xl": "https://cdn-images.dzcdn.net/images/cover/6234a4d5e58e685debdcd8697b671cb0/1000x1000-000000-80-0-0.jpg",
        "md5_image": "6234a4d5e58e685debdcd8697b671cb0",
        "tracklist": "https://api.deezer.com/album/1428148/tracks",
        "type": "album"
      },
      "type": "track"
    },
    {
      "id": 43377901,
      "readable": true,
      "title": "Let It Be (Tribute to the Beatles)",
      "title_short": "Let It Be",
      "title_version": "(Tribute to the Beatles)",
      "link": "https://www.deezer.com/track/43377901",
      "duration": 257,
      "rank": 45017,
      "explicit_lyrics": false,
      "explicit_content_lyrics": 0,
      "explicit_content_cover": 2,
      "preview": "https://cdnt-preview.dzcdn.net/api/1/1/6/4/7/0/6477f246f7ca0e66c4871869f9b7a81d.mp3?hdnea=exp=1746810790~acl=/api/1/1/6/4/7/0/6477f246f7ca0e66c4871869f9b7a81d.mp3*~data=user_id=0,application_id=42~hmac=3626c8827ce10ca6da1fec3ded5f4f9166e129b108be8b74dd18968df5666663",
      "md5_image": "00c0c296168f902cb1e9c8df7b04ab7d",
      "artist": {
        "id": 3105211,
        "name": "Tactus Rosa",
        "link": "https://www.deezer.com/artist/3105211",
        "picture": "https://api.deezer.com/artist/3105211/image",
        "picture_small": "https://cdn-images.dzcdn.net/images/artist/00c0c296168f902cb1e9c8df7b04ab7d/56x56-000000-80-0-0.jpg",
        "picture_medium": "https://cdn-images.dzcdn.net/images/artist/00c0c296168f902cb1e9c8df7b04ab7d/250x250-000000-80-0-0.jpg",
        "picture_big": "https://cdn-images.dzcdn.net/images/artist/00c0c296168f902cb1e9c8df7b04ab7d/500x500-000000-80-0-0.jpg",
        "picture_xl": "https://cdn-images.dzcdn.net/images/artist/00c0c296168f902cb1e9c8df7b04ab7d/1000x1000-000000-80-0-0.jpg",
        "tracklist": "https://api.deezer.com/artist/3105211/top?limit=50",
        "type": "artist"
      },
      "album": {
        "id": 4244631,
        "title": "The Beatles & Pink Floyd... Oltre il già noto (Acoustic Symphony Band)",
        "cover": "https://api.deezer.com/album/4244631/image",
        "cover_small": "https://cdn-images.dzcdn.net/images/cover/00c0c296168f902cb1e9c8df7b04ab7d/56x56-000000-80-0-0.jpg",
        "cover_medium": "https://cdn-images.dzcdn.net/images/cover/00c0c296168f902cb1e9c8df7b04ab7d/250x250-000000-80-0-0.jpg",
        "cover_big": "https://cdn-images.dzcdn.net/images/cover/00c0c296168f902cb1e9c8df7b04ab7d/500x500-000000-80-0-0.jpg",
        "cover_xl": "https://cdn-images.dzcdn.net/images/cover/00c0c296168f902cb1e9c8df7b04ab7d/1000x1000-000000-80-0-0.jpg",
        "md5_image": "00c0c296168f902cb1e9c8df7b04ab7d",
        "tracklist": "https://api.deezer.com/album/4244631/tracks",
        "type": "album"
      },
      "type": "track"
    }
  ],
  "total": 292,
  "next": "https://api.deezer.com/search?q=beatles%20let%20it%20be&redirect_uri=http%253A%252F%252Fguardian.mashape.com%252Fcallback&index=25"
}




